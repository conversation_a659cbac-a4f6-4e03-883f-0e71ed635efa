// eslint.config.js
import js from '@eslint/js';
import stylistic from '@stylistic/eslint-plugin';

import ts from '@typescript-eslint/eslint-plugin';
import tsParser from '@typescript-eslint/parser';
import importPlugins from 'eslint-plugin-import';
import prettierPlugin from 'eslint-plugin-prettier';
import sortImportSimple from 'eslint-plugin-simple-import-sort';
import sortClassMembers from 'eslint-plugin-sort-class-members';
import globals from 'globals';

// Xác định patterns cần bỏ qua
const ignoredPaths = [
	"**/node_modules/**",
	"**/_internal/**",
	"**/generated/**",
];

// C<PERSON>u hình cơ bản
export default [
	// Thiết lập bỏ qua chính
	{
		ignores: ignoredPaths,
	},

	// C<PERSON>u hình chung cho TypeScript
	{
		files: ['**/*.ts'],
		plugins: {
			'@typescript-eslint': ts,
			import: importPlugins,
			'simple-import-sort': sortImportSimple,
			'sort-class-members': sortClassMembers,
			'@stylistic': stylistic,
			prettier: prettierPlugin,
		},
		languageOptions: {
			parser: tsParser,
			parserOptions: {
				project: './tsconfig.json',
				sourceType: 'module',
				ecmaVersion: 'lastest',
			},
			globals: {
				...globals.node,
			},
		},
		settings: {
			'import/resolver': {
				typescript: {
					alwaysTryTypes: true,
					project: './tsconfig.json',
				},
				node: true,
			},
		},
		rules: {
			...ts.configs.recommended.rules,
			'@typescript-eslint/no-unsafe-function-type': 'off',
			'@typescript-eslint/no-unused-expressions': 'off',
			'prettier/prettier': 'off',
			'@typescript-eslint/ban-ts-comment': 'off',
			'@typescript-eslint/no-empty-object-type': 'off',

			// Spacing and formatting rules
			'@stylistic/indent': ['error', 'tab'],
			'@stylistic/quotes': ['error', 'single'],
			'@stylistic/semi': ['error', 'always'],
			'@stylistic/comma-dangle': ['error', 'only-multiline'],

			// TypeScript specific rules
			'@typescript-eslint/no-unused-vars': [
				'off',
				{
					argsIgnorePattern: '^_',
					varsIgnorePattern: '^_',
				},
			],
			'@typescript-eslint/no-explicit-any': 'off',

			// Import sorting and organization
			'simple-import-sort/imports': ['error'],
			'simple-import-sort/exports': 'error',

			// Internal module import restrictions
			'no-restricted-imports': [
				'error',
				{
					patterns: [
						{
							group: ['**/_internal/**', '@/*/_internal/**', '@/**/_internal/**'],
							message:
								'Không được phép import trực tiếp từ thư mục _internal. Hãy sử dụng các export từ file index.ts.',
						},
					],
				},
			],
		},
	},

	// Cấu hình cho JavaScript
	{
		files: ['**/*.js', '**/*.mjs', '**/*.cjs'],
		plugins: {
			prettier: prettierPlugin,
			stylistic,
		},
		languageOptions: {
			ecmaVersion: 2022,
			sourceType: 'module',
			globals: {
				...globals.node,
			},
		},
		settings: {
			'import/resolver': {
				node: true,
			},
		},
		rules: {
			...js.configs.recommended.rules,
			'prettier/prettier': 'error',
		},
	},
];
