import { BaseTableWithUser } from '../base';
import { UUID } from '../common';

export type {
  Account,
  AccountBase,
  AccountWithRelations,
  CreateAccountInput,
  UpdateAccountInput,
  AccountFilterInput,
  AccountPaymentAppLink,
  AccountDebitCardLink
};

/**
 * Base interface for Account
 * Matches Account model in Prisma schema
 */
interface AccountBase extends BaseTableWithUser {
  name: string;
  description: string | null;
  accountTypeId: UUID;
  balance: number;
  currency: string;
  color: string;
  contactId: UUID | null;
}

/**
 * Account with relations
 */
interface AccountWithRelations extends AccountBase {
  accountType: AccountTypeBase;
  contact: ContactBase | null;
  bankInfo: BankInfoBase | null;
  debitCards: DebitCardBase[];
  accountDebitCardLinks: AccountDebitCardLink[];
  accountPaymentAppLinks: AccountPaymentAppLink[];
}

/**
 * Union type for Account
 */
type Account = AccountBase | AccountWithRelations;

/**
 * Input type for creating a new account
 */
interface CreateAccountInput {
  name: string;
  description?: string | null;
  accountTypeId: UUID;
  balance?: number;
  currency?: string;
  color?: string;
  contactId?: UUID | null;
  isActive?: boolean;
  userId: UUID;
}

/**
 * Input type for updating an existing account
 */
interface UpdateAccountInput {
  id: UUID;
  name?: string;
  description?: string | null;
  accountTypeId?: UUID;
  balance?: number;
  currency?: string;
  color?: string;
  contactId?: UUID | null;
  isActive?: boolean;
}

/**
 * Input type for filtering accounts
 */
interface AccountFilterInput {
  search?: string;
  accountTypeId?: UUID;
  contactId?: UUID | null;
  currency?: string;
  isActive?: boolean;
  minBalance?: number;
  maxBalance?: number;
  hasBankInfo?: boolean;
  hasContact?: boolean;
}

/**
 * Represents a link between an account and a payment app
 * Matches AccountPaymentAppLink model in Prisma schema
 */
interface AccountPaymentAppLink {
  id: UUID;
  accountId: UUID;
  paymentAppId: UUID;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

/**
 * Represents a link between an account and a debit card
 * Matches AccountDebitCardLink model in Prisma schema
 */
interface AccountDebitCardLink {
  id: UUID;
  accountId: UUID;
  debitCardId: UUID;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
}

// Forward declarations to avoid circular imports
interface AccountTypeBase {
  id: UUID;
  name: string;
  type: string;
}

interface BankInfoBase {
  id: UUID;
  accountId: UUID;
  bankName: string | null;
}

interface DebitCardBase {
  id: UUID;
  cardNumber: string | null;
  userId: UUID;
}

interface ContactBase {
  id: UUID;
  name: string;
  contactTypeId: UUID;
}
