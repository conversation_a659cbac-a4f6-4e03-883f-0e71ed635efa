import { BaseTableWithUser } from '../base';
import { CategoryType, UUID } from '../common';

export type {
  Category,
  CategoryBase,
  CategoryWithRelations,
  CreateCategoryInput,
  UpdateCategoryInput,
  MoveCategoryInput,
  CategoryFilterInput,
  CategoryResponse
};

/**
 * Base interface for Category
 * Matches Category model in Prisma schema
 */
interface CategoryBase extends BaseTableWithUser {
  name: string;
  description: string | null;
  type: CategoryType;
  color: string;
  parentId: UUID | null;
  isDefault: boolean;
}

/**
 * Category with relations
 */
interface CategoryWithRelations extends CategoryBase {
  parent: CategoryBase | null;
  children: CategoryBase[];
  categoryGoals: CategoryGoalBase[];
}

/**
 * Union type for Category
 */
type Category = CategoryBase | CategoryWithRelations;

/**
 * Input for creating a new category
 */
interface CreateCategoryInput {
  name: string;
  description?: string | null;
  type: CategoryType;
  color?: string;
  parentId?: UUID | null;
  isDefault?: boolean;
  isActive?: boolean;
  userId: UUID;
}

/**
 * Input for updating an existing category
 */
interface UpdateCategoryInput {
  id: UUID;
  name?: string;
  description?: string | null;
  type?: CategoryType;
  color?: string;
  parentId?: UUID | null;
  isDefault?: boolean;
  isActive?: boolean;
}

/**
 * Input for moving a category in the hierarchy
 */
interface MoveCategoryInput {
  id: UUID;
  parentId: UUID | null;
  index: number;
}

/**
 * Input for filtering categories
 */
interface CategoryFilterInput {
  search?: string;
  type?: CategoryType;
  parentId?: UUID | null;
  isDefault?: boolean;
  isActive?: boolean;
  includeChildren?: boolean;
  userId?: UUID;
}

/**
 * Response type for category operations
 */
interface CategoryResponse {
  success: boolean;
  message?: string;
  category?: Category;
  errors?: Array<{ message: string; field?: string }>;
}

// Forward declaration to avoid circular import
interface CategoryGoalBase {
  id: UUID;
  name: string | null;
  categoryId: UUID | null;
}
