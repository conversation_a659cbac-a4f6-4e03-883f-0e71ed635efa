{"name": "money-management", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "db:reset": "prisma migrate reset", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "analyze": "ANALYZE=true npm run build", "analyze:bundle": "node scripts/analyze-bundle.js", "perf:audit": "lighthouse http://localhost:3000 --output=html --output-path=./lighthouse-report.html", "perf:monitor": "npm run analyze:bundle && npm run perf:audit"}, "dependencies": {"@apollo/client": "^3.13.9", "@apollo/server": "^5.0.0", "@as-integrations/next": "^4.0.0", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^6.12.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "graphql": "^16.11.0", "graphql-scalars": "^1.24.2", "graphql-tag": "^2.12.6", "input-otp": "1.4.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.3.5", "next-themes": "latest", "prisma": "^6.12.0", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "latest", "shadcn": "latest", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/jsonwebtoken": "^9.0.10", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "eslint": "^9.32.0", "eslint-config-next": "^15.4.4", "eslint-config-prettier": "^10.1.8", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-unused-imports": "^4.1.4", "postcss": "^8.5", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^3.4.17", "tsx": "^4.20.3", "typescript": "^5", "@eslint/js": "^9.26.0", "@stylistic/eslint-plugin": "^4.2.0", "@stylistic/eslint-plugin-js": "^4.2.0", "@types/axios": "^0.9.36", "@types/chrome-remote-interface": "^0.31.14", "@types/minimist": "^1.2.5", "eslint-plugin-import-access": "^3.0.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-sort-class-members": "^1.21.0", "globals": "^16.1.0", "prettier": "^3.5.3", "typescript-eslint": "^8.32.1"}}