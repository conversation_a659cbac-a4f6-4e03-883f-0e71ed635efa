/**
 * Common GraphQL types and interfaces used across the application
 */

// Scalar types
export type UUID = string;
export type DateTime = string; // ISO 8601 string format
export type JSON = Record<string, any>;

// Enums from Prisma schema - Updated to match exactly with schema.prisma
export enum Language {
  VI = 'vi',
  EN = 'en',
  ZH = 'zh',
  JA = 'ja',
  KO = 'ko'
}

export enum Theme {
  LIGHT = 'light',
  DARK = 'dark',
  SYSTEM = 'system'
}

export enum CategoryType {
  INCOME = 'income',
  EXPENSE = 'expense',
  TRANSFER = 'transfer',
  NONE = 'none'
}

export enum TransactionType {
  INCOME = 'income',
  EXPENSE = 'expense',
  TRANSFER = 'transfer'
}

export enum AuditAction {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  RESTORE = 'restore'
}

export enum Period {
  NONE = 'none',
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
  CUSTOM = 'custom'
}

export enum AccountTypeType {
  NONE = 'none',
  BANK = 'bank',
  PAYMENT_APP = 'payment_app'
}

// Additional common types
export type CurrencyCode = string; // e.g., 'VND', 'USD', 'EUR'

// Pagination types
export interface Connection<T> {
  edges: Array<{
    node: T;
    cursor: string;
  }>;
  pageInfo: {
    hasNextPage: boolean;
    hasPreviousPage: boolean;
    startCursor?: string;
    endCursor?: string;
  };
  totalCount: number;
}

export interface PageInfo {
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  startCursor?: string;
  endCursor?: string;
}

export interface Edge<T> {
  node: T;
  cursor: string;
}

// Common input types
export interface PaginationInput {
  first?: number;
  after?: string;
  last?: number;
  before?: string;
}

export interface SortInput {
  field: string;
  direction: 'ASC' | 'DESC';
}

// Common response types
export interface BaseResponse {
  success: boolean;
  message?: string;
  errors?: Array<{ message: string; field?: string }>;
}

// Common filter input
export interface DateRangeInput {
  from?: DateTime;
  to?: DateTime;
}

// Common relation types
export type WithTimestamps = {
  createdAt: DateTime;
  updatedAt: DateTime;
  deletedAt?: DateTime;
};

export type WithSoftDelete = {
  isActive: boolean;
  deletedAt?: DateTime;
};

export type OmitOnEdit<T, K extends keyof T = never> = Omit<T, 'createdAt' | 'updatedAt' | 'deletedAt' | K>;
export type OmitOnCreate<T, K extends keyof T = never> = Omit<T, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt' | K>;
