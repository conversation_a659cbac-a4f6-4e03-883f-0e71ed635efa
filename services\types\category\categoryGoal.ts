
import { BaseTableWithUser } from '../base';
import { DateTime, Period, UUID } from '../common';

export type {
  CategoryGoal,
  CategoryGoalBase,
  CategoryGoalWithRelations,
  CreateCategoryGoalInput,
  UpdateCategoryGoalInput,
  CategoryGoalFilterInput,
  UpdateCategoryGoalProgressInput,
  CategoryGoalResponse
};

/**
 * Base interface for CategoryGoal
 * Matches CategoryGoal model in Prisma schema
 */
interface CategoryGoalBase extends BaseTableWithUser {
  name: string | null;
  description: string | null;
  targetAmount: number | null;
  period: Period | null;
  startDate: DateTime | null;
  endDate: DateTime | null;
  transferFrom: string | null; // Account ID to transfer from
  transferTo: string | null; // Account ID to transfer to
  isRecurring: boolean;
  isDefault: boolean;
  categoryId: UUID | null;
  accountId: UUID | null;
}

/**
 * CategoryGoal with relations
 */
interface CategoryGoalWithRelations extends CategoryGoalBase {
  category: CategoryBase | null;
  account: AccountBase | null;
}

/**
 * Union type for CategoryGoal
 */
type CategoryGoal = CategoryGoalBase | CategoryGoalWithRelations;

/**
 * Input type for creating a new category goal
 */
interface CreateCategoryGoalInput {
  name?: string | null;
  description?: string | null;
  targetAmount?: number | null;
  period?: Period | null;
  startDate?: DateTime | null;
  endDate?: DateTime | null;
  transferFrom?: string | null;
  transferTo?: string | null;
  isRecurring?: boolean;
  isDefault?: boolean;
  categoryId?: UUID | null;
  accountId?: UUID | null;
  isActive?: boolean;
  userId: UUID;
}

/**
 * Input type for updating a category goal
 */
interface UpdateCategoryGoalInput {
  id: UUID;
  name?: string | null;
  description?: string | null;
  targetAmount?: number | null;
  period?: Period | null;
  startDate?: DateTime | null;
  endDate?: DateTime | null;
  transferFrom?: string | null;
  transferTo?: string | null;
  isRecurring?: boolean;
  isDefault?: boolean;
  categoryId?: UUID | null;
  accountId?: UUID | null;
  isActive?: boolean;
}

/**
 * Input type for category goal filters
 */
interface CategoryGoalFilterInput {
  search?: string;
  categoryId?: UUID;
  period?: Period;
  isActive?: boolean;
  isDefault?: boolean;
  startDateBefore?: DateTime;
  startDateAfter?: DateTime;
  endDateBefore?: DateTime;
  endDateAfter?: DateTime;
}

/**
 * Input type for updating category goal progress
 */
interface UpdateCategoryGoalProgressInput {
  id: UUID;
  amount: number;
  isIncrement?: boolean;
}

/**
 * Response type for category goal operations
 */
interface CategoryGoalResponse {
  success: boolean;
  message?: string;
  categoryGoal?: CategoryGoal;
  errors?: Array<{ message: string; field?: string }>;
}

// Forward declarations to avoid circular imports
interface CategoryBase {
  id: UUID;
  name: string;
  type: string;
}

interface AccountBase {
  id: UUID;
  name: string;
  userId: UUID;
}


