import { BaseTable, BaseTableActiveable } from './base';
import { DateTime, Language, Theme, UUID } from './common';

/**
 * User role type - matches Prisma schema
 */
export type UserRole = 'admin' | 'user';

/**
 * Base interface for UserInformation
 * Matches UserInformation model in Prisma schema
 */
export interface UserInformationBase extends BaseTable {
  userId: UUID;
  lastLogin: DateTime | null;
  loginCount: number;
  ipAddress: string | null;
  userAgent: string | null;
}

/**
 * UserInformation with relations
 */
export interface UserInformationWithRelations extends UserInformationBase {
  user: UserBase;
}

/**
 * Union type for UserInformation
 */
export type UserInformation = UserInformationBase | UserInformationWithRelations;

/**
 * Base interface for User
 * Matches User model in Prisma schema
 */
export interface UserBase extends BaseTableActiveable {
  email: string;
  password: string; // hashed
  role: string; // 'admin' | 'user' - using string to match Prisma
  isVerified: boolean;
  lastLoginAt: DateTime | null;
}

/**
 * User with basic relations (avoiding circular imports)
 */
export interface UserWithRelations extends UserBase {
  userInformation?: UserInformation | null;
  preferences?: UserPreferences | null;
}

/**
 * Union type for User
 */
export type User = UserBase | UserWithRelations;

/**
 * Base interface for UserPreferences
 * Matches UserPreferences model in Prisma schema
 */
export interface UserPreferencesBase extends BaseTableActiveable {
  name: string;
  description: string | null;
  userId: UUID;
  currency: string;
  language: Language;
  theme: Theme;
  dateFormat: string | null;
  numberFormat: string | null;
  timezone: string | null;
}

/**
 * UserPreferences with relations
 */
export interface UserPreferencesWithRelations extends UserPreferencesBase {
  user: UserBase;
}

/**
 * Union type for UserPreferences
 */
export type UserPreferences = UserPreferencesBase | UserPreferencesWithRelations;

// Input types for User mutations

/**
 * Input for creating a new user
 */
export interface CreateUserInput {
  email: string;
  password: string;
  role?: string;
  isActive?: boolean;
  isVerified?: boolean;
  preferences?: CreateUserPreferencesInput;
}

/**
 * Input for updating an existing user
 */
export interface UpdateUserInput {
  id: UUID;
  email?: string;
  password?: string; // Hashed on the server
  role?: string;
  isActive?: boolean;
  isVerified?: boolean;
  lastLoginAt?: DateTime | null;
}

/**
 * Input for creating user preferences
 */
export interface CreateUserPreferencesInput {
  name: string;
  description?: string | null;
  currency?: string;
  language?: Language;
  theme?: Theme;
  dateFormat?: string | null;
  numberFormat?: string | null;
  timezone?: string | null;
  isActive?: boolean;
}

/**
 * Input for updating user preferences
 */
export interface UpdateUserPreferencesInput {
  id: UUID;
  name?: string;
  description?: string | null;
  currency?: string;
  language?: Language;
  theme?: Theme;
  dateFormat?: string | null;
  numberFormat?: string | null;
  timezone?: string | null;
  isActive?: boolean;
}

/**
 * Input for user login
 */
export interface LoginInput {
  email: string;
  password: string;
  rememberMe?: boolean;
}

/**
 * Input for changing password
 */
export interface ChangePasswordInput {
  currentPassword: string;
  newPassword: string;
}

/**
 * Input for resetting password
 */
export interface ResetPasswordInput {
  token: string;
  newPassword: string;
}

/**
 * Input for requesting password reset
 */
export interface ForgotPasswordInput {
  email: string;
}

// Response types
/**
 * Authentication response type
 */
export interface AuthResponse {
  token: string;
  user: User;
  expiresIn: number;
}
