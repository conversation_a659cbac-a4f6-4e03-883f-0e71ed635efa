import { AuditAction, DateTime, JSON, UUID } from './common';

export type {
  AuditLog,
  AuditLogBase,
  AuditLogWithRelations,
  CreateAuditLogInput,
  UpdateAuditLogInput,
  AuditLogFilterInput
};

/**
 * Base interface for AuditLog
 * Matches AuditLog model in Prisma schema
 */
interface AuditLogBase {
  id: UUID;
  action: AuditAction;
  tableName: string;
  recordId: UUID;
  oldValues: JSON | null;
  newValues: JSON | null;
  userId: UUID;
  ipAddress: string | null;
  userAgent: string | null;
  createdAt: DateTime;
}

/**
 * AuditLog with relations
 */
interface AuditLogWithRelations extends AuditLogBase {
  user: UserBase;
}

/**
 * Union type for AuditLog
 */
type AuditLog = AuditLogBase | AuditLogWithRelations;

/**
 * Input for creating a new audit log
 */
interface CreateAuditLogInput {
  action: AuditAction;
  tableName: string;
  recordId: UUID;
  oldValues?: JSON | null;
  newValues?: JSON | null;
  userId: UUID;
  ipAddress?: string | null;
  userAgent?: string | null;
}

/**
 * Input for updating an existing audit log
 * Note: AuditLog is typically immutable, so updates are rare
 */
interface UpdateAuditLogInput {
  id: UUID;
  // Usually no fields are updatable for audit logs
}

/**
 * Input for filtering audit logs
 */
interface AuditLogFilterInput {
  search?: string;
  userId?: UUID;
  action?: AuditAction;
  tableName?: string;
  recordId?: UUID;
  startDate?: DateTime;
  endDate?: DateTime;
}

// Forward declaration to avoid circular import
interface UserBase {
  id: UUID;
  email: string;
}
