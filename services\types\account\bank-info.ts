import { BaseTableActiveable } from '../base';
import { UUID } from '../common';

export type {
  BankInfo,
  BankInfoBase,
  BankInfoWithRelations,
  CreateBankInfoInput,
  UpdateBankInfoInput,
  BankInfoFilterInput
};

/**
 * Base interface for BankInfo
 * Matches BankInfo model in Prisma schema
 */
interface BankInfoBase extends BaseTableActiveable {
  userId: UUID;
  accountId: UUID;
  bankName: string | null;
  bankCode: string | null;
  accountNumber: string | null;
  accountHolderName: string | null;
  branchName: string | null;
  note: string | null;
}

/**
 * BankInfo with relations
 */
interface BankInfoWithRelations extends BankInfoBase {
  user: UserBase;
  account: AccountBase;
}

/**
 * Union type for BankInfo
 */
type BankInfo = BankInfoBase | BankInfoWithRelations;

/**
 * Input for creating a new bank info
 */
interface CreateBankInfoInput {
  userId: UUID;
  accountId: UUID;
  bankName?: string | null;
  bankCode?: string | null;
  accountNumber?: string | null;
  accountHolderName?: string | null;
  branchName?: string | null;
  note?: string | null;
  isActive?: boolean;
}

/**
 * Input for updating an existing bank info
 */
interface UpdateBankInfoInput {
  id: UUID;
  bankName?: string | null;
  bankCode?: string | null;
  accountNumber?: string | null;
  accountHolderName?: string | null;
  branchName?: string | null;
  note?: string | null;
  isActive?: boolean;
}

/**
 * Input for filtering bank info
 */
interface BankInfoFilterInput {
  search?: string;
  userId?: UUID;
  accountId?: UUID;
  bankName?: string;
  bankCode?: string;
  accountNumber?: string;
  accountHolderName?: string;
  branchName?: string;
  isActive?: boolean;
}

// Forward declarations to avoid circular imports
interface UserBase {
  id: UUID;
  email: string;
}

interface AccountBase {
  id: UUID;
  name: string;
  userId: UUID;
}
