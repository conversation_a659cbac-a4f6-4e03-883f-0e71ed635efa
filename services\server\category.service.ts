import { Category, CategoryGoal, CategoryType } from '@/lib/generated/prisma';
import prisma from '@/lib/prisma';
import { BaseService } from './base.service';
import { defaultPagination } from './default';
import { BasePaginationParams, CreateIgnoredField, UpdateIgnoredField } from './types';
import z from 'zod';

export interface CategoryPaginationParams extends BasePaginationParams {
	isActive?: boolean;
	type?: CategoryType;
	userId?: string;
	search?: string;
	sortBy?: 'name' | 'createdAt' | 'updatedAt';
}

export interface CategoryWithGoal extends Category {
	categoryGoal?: CategoryGoal;
}

export interface CreateFormCategoryWithGoal extends Omit<Category, CreateIgnoredField> {
	categoryGoal?: Omit<CategoryGoal, CreateIgnoredField>;
}

export interface UpdateFormCategoryWithGoal extends Omit<Category, UpdateIgnoredField> {
	categoryGoal?: Omit<CategoryGoal, UpdateIgnoredField>;
}

export default class CategoryService extends BaseService {
	static updateStatus(id: string, isActive: boolean) {
		return prisma.category.update({
			where: { id },
			data: { isActive: !!isActive },
		});
	}
	static async getCategoriesWithGoal(params: CategoryPaginationParams): Promise<CategoryWithGoal[]> {
		const { sort, sortBy, isActive, type, userId, search, page, limit } = params;
		const skip = ((page ?? defaultPagination.page) - 1) * (limit ?? defaultPagination.limit);
		const take = limit ?? defaultPagination.limit;
		const orderBy = sortBy ? { [sortBy]: sort } : undefined;

		const categories = await prisma.category.findMany({
			where: {
				isActive,
				OR: search
					? [
							{
								name: {
									contains: search,
									mode: 'insensitive',
								},
							},
						]
					: undefined,
				...(type ? { type } : {}),
				...(userId ? { userId } : {}),
			},
			orderBy,
			skip,
			take,
			include: {
				user: false,
				categoryGoals: {
					where: {
						isDefault: true,
					},
					take: 1,
				},
			},
		});

		return categories.map(category => {
			const categoryGoal = category.categoryGoals[0];
			if (categoryGoal) {
				// @ts-ignore
				delete category.categoryGoals;
			}
			return {
				...category,
				categoryGoal,
			};
		});
	}

	/**
	 * this function will ctaye a Category, then create a new CategoryGoal for it then set it isDefault
	 * @param input
	 * @returns
	 */
	static async createCategoryWithGoal(input: CreateFormCategoryWithGoal): Promise<CategoryWithGoal> {
		const { categoryGoal: categoryGoalData, ...categoryData } = input;
		const category = await prisma.category.create({
			data: {
				...categoryData,
			},
		});
		const categoryGoal = await prisma.categoryGoal.create({
			data: {
				...categoryGoalData,
				categoryId: category.id,
				userId: category.userId || categoryData.userId,
				isDefault: true,
			},
		});
		return {
			...category,
			categoryGoal,
		};
	}
	/**
	 * this function will update a Category,
	 * then update a new CategoryGoal for it then set it isDefault
	 * (if categoryGoalData dont have id, create a new CategoryGoal )
	 * @param input
	 * @returns
	 */
	static async updateCategoryWithGoal(input: UpdateFormCategoryWithGoal): Promise<CategoryWithGoal> {
		const { categoryGoal: categoryGoalData, ...categoryData } = input;

		const category = await prisma.category.update({
			where: { id: categoryData.id },
			data: { ...categoryData },
		});

		if (!categoryGoalData) {
			return category;
		}

		// Ensure all other goals for this category are not default
		await prisma.categoryGoal.updateMany({
			where: {
				categoryId: category.id,
				isDefault: true,
				...(categoryGoalData.id ? { id: { not: categoryGoalData.id } } : {}),
			},
			data: {
				isDefault: false,
			},
		});

		let categoryGoal;
		if (categoryGoalData.id) {
			// Update existing goal
			categoryGoal = await prisma.categoryGoal.update({
				where: { id: categoryGoalData.id },
				data: {
					...categoryGoalData,
					categoryId: category.id,
					userId: category.userId || categoryData.userId,
					isDefault: true,
				},
			});
		} else {
			// Create new goal
			categoryGoal = await prisma.categoryGoal.create({
				data: {
					...categoryGoalData,
					categoryId: category.id,
					userId: category.userId || categoryData.userId,
					isDefault: true,
				},
			});
		}

		return {
			...category,
			categoryGoal,
		};
	}
}
