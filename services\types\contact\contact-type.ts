import { BaseTableActiveable } from '../base';
import { UUID } from '../common';

/**
 * Base interface for ContactType
 * Matches ContactType model in Prisma schema
 */
export interface ContactTypeBase extends BaseTableActiveable {
  name: string;
  description: string | null;
  color: string;
}

/**
 * ContactType with relations
 */
export interface ContactTypeWithRelations extends ContactTypeBase {
  contacts: ContactBase[];
}

/**
 * Union type for ContactType
 */
export type ContactType = ContactTypeBase | ContactTypeWithRelations;

// Input types for ContactType mutations

/**
 * Input for creating a new contact type
 */
export interface CreateContactTypeInput {
  name: string;
  description?: string | null;
  color?: string;
  isActive?: boolean;
}

/**
 * Input for updating an existing contact type
 */
export interface UpdateContactTypeInput {
  id: UUID;
  name?: string;
  description?: string | null;
  color?: string;
  isActive?: boolean;
}

/**
 * Input for filtering contact types
 */
export interface ContactTypeFilterInput {
  search?: string;
  isActive?: boolean;
}

// Forward declaration to avoid circular import
interface ContactBase {
  id: UUID;
  name: string;
  contactTypeId: UUID;
}
