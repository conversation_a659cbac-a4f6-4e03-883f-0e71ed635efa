{
	"editor.defaultFormatter": "esbenp.prettier-vscode",
	"editor.tabSize": 8,
	"editor.insertSpaces": false,
	"editor.detectIndentation": false,
	"editor.renderWhitespace": "none",
	"typescript.preferences.importModuleSpecifier": "non-relative",
	"typescript.preferences.importModuleSpecifierEnding": "js",
	"typescript.tsdk": "node_modules/typescript/lib",
	"typescript.updateImportsOnFileMove.enabled": "always",
	"typescript.preferences.quoteStyle": "single",
	"javascript.preferences.quoteStyle": "single",
	"typescript.format.semicolons": "insert",
	"javascript.format.semicolons": "insert",

	"editor.codeActionsOnSave": {
	  "source.fixAll.eslint": "explicit"
	},
	"[typescript]": {
	  "editor.insertSpaces": false,
	  "editor.tabSize": 8,
	  "editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"[javascript]": {
	  "editor.insertSpaces": false,
	  "editor.tabSize": 8,
	  "editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"[json]": {
	  "editor.insertSpaces": false,
	  "editor.tabSize": 8,
	  "editor.defaultFormatter": "esbenp.prettier-vscode"
	},
	"prettier.useTabs": true,
	"files.trimTrailingWhitespace": true,
	"files.insertFinalNewline": true,
	"code-runner.executorMap": {
	  "typescript": "npx tsx"
	},
	"typescript.preferences.autoImportFileExcludePatterns": [
		"**/node_modules/**",
		"**/_internal/**"
	],
	"javascript.preferences.autoImportFileExcludePatterns": [
		"**/node_modules/**",
		"**/_internal/**"
	],
	"highlight.regexes": {
	// "(console\\.log\\(.*\\))": {
	//   "regexFlags": "g",
	//   "filterLanguageRegex": "^(javascript|typescript)$",
	//   "decorations": [
	//     {
	//       "color": "#88888899"
	//     }
	//   ]
	// },
	// "(console\\.warn\\(.*\\))": {
	//   "regexFlags": "g",
	//   "filterLanguageRegex": "^(javascript|typescript)$",
	//   "decorations": [
	//     {
	//       "color": "#f2f56199"
	//     }
	//   ]
	// },
	// "(console\\.error\\(.*\\))": {
	//   "regexFlags": "g",
	//   "filterLanguageRegex": "^(javascript|typescript)$",
	//   "decorations": [
	//     {
	//       "color": "#ff929299"
	//     }
	//   ]
	// },
	// - debug (xám trung tính)
	"(\\b[\\w$]+\\.(?:taskLogger|logger)\\.debug\\([^)]*\\))": {
		"regexFlags": "g",
		"filterLanguageRegex": "^(javascript|typescript|typescriptreact|javascriptreact)$",
		"decorations": [{ "color": "#88888899" }]
	      },

	      // info (xanh dương nhẹ)
	      "(\\b[\\w$]+\\.(?:taskLogger|logger)\\.info\\([^)]*\\))": {
		"regexFlags": "g",
		"filterLanguageRegex": "^(javascript|typescript|typescriptreact|javascriptreact)$",
		"decorations": [{ "color": "#88888899" }]
	      },

	      // warn (cam ấm vừa phải)
	      "(\\b[\\w$]+\\.(?:taskLogger|logger)\\.warn\\([^)]*\\))": {
		"regexFlags": "g",
		"filterLanguageRegex": "^(javascript|typescript|typescriptreact|javascriptreact)$",
		"decorations": [{ "color": "#F4A26199" }]
	      },

	      // error (đỏ đất êm dịu)
	      "(\\b[\\w$]+\\.(?:taskLogger|logger)\\.error\\([^)]*\\))": {
		"regexFlags": "g",
		"filterLanguageRegex": "^(javascript|typescript|typescriptreact|javascriptreact)$",
		"decorations": [{ "color": "#E76F5199" }]
	      },
	      // note: `…` (template literal, bao gồm cả ${…})
	      "(note\\s*:\\s*(?:'[^']*'|\"[^\"]*\"|`[\\s\\S]*?`))": {
		"regexFlags": "g",
		"filterLanguageRegex": "^(javascript|typescript|javascriptreact|typescriptreact)$",
		"decorations": [
		  { "color": "#88888899" }
		]
	      }
}}
