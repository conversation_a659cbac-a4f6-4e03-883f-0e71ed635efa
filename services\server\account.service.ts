import { Account, DebitCard, PaymentApp } from '@/lib/generated/prisma';
import { BaseService } from './base.service';
import { defaultPagination } from './default';
import prisma from '@/lib/prisma.js';
import { BasePaginationParams, CreateIgnoredField, UpdateIgnoredField } from './types';
import { NotFoundError } from '@/lib/api/utils.js';

export interface AccountPaginationParams extends BasePaginationParams {
	isActive?: boolean;
	userId?: string;
	search?: string;
	sortBy?: 'name' | 'createdAt' | 'updatedAt';
	sort?: 'asc' | 'desc';
}

export interface AccountWithLinked extends Account {
	paymentApps: PaymentApp[];
	debitCards: DebitCard[];
}

export interface CreateAccountWithLinked extends Omit<Account, CreateIgnoredField> {
	paymentApps?: string[]; // payment app ids
	debitCards?: string[]; // debit card ids
}

export interface UpdateAccountWithLinked extends Omit<Account, UpdateIgnoredField> {
	paymentApps?: string[]; // payment app ids
	debitCards?: string[]; // debit card ids
	removePaymentApps?: string[]; // payment app ids
	removeDebitCards?: string[]; // debit card ids
}
function validIds(ids?: string[]) {
	return ids && ids.length > 0;
}

export default class AccountService extends BaseService {
	static async getAccountWithLinked(id: string): Promise<AccountWithLinked | null> {
		return await prisma.account
			.findUnique({
				where: { id },
				include: {
					user: false,
					accountPaymentAppLinks: {
						include: {
							paymentApp: true,
						},
					},
					accountDebitCardLinks: {
						include: {
							debitCard: true,
						},
					},
				},
			})
			.then(account => {
				return account
					? {
							...account,
							paymentApps: account.accountPaymentAppLinks.map(link => link.paymentApp),
							debitCards: account.accountDebitCardLinks.map(link => link.debitCard),
						}
					: account;
			});
	}
	static async getAccountsWithLinked(params: AccountPaginationParams): Promise<AccountWithLinked[]> {
		const { sort, sortBy, isActive, userId, search, page, limit } = params;
		const skip = ((page ?? defaultPagination.page) - 1) * (limit ?? defaultPagination.limit);
		const take = limit ?? defaultPagination.limit;
		const orderBy = sortBy ? { [sortBy]: sort } : undefined;

		const accounts = await prisma.account.findMany({
			where: {
				isActive,
				OR: search
					? [
							{
								name: {
									contains: search,
									mode: 'insensitive',
								},
							},
						]
					: undefined,
				...(userId ? { userId } : {}),
			},
			orderBy,
			skip,
			take,
			include: {
				user: false,
				accountPaymentAppLinks: {
					include: {
						paymentApp: true,
					},
				},
				accountDebitCardLinks: {
					include: {
						debitCard: true,
					},
				},
			},
		});

		return accounts.map(account => ({
			...account,
			paymentApps: account.accountPaymentAppLinks.map(link => link.paymentApp),
			debitCards: account.accountDebitCardLinks.map(link => link.debitCard),
		}));
	}

	static async createAccountWithLinked(data: CreateAccountWithLinked): Promise<AccountWithLinked> {
		const { paymentApps: paymentAppIds, debitCards: debitCardIds, ...accountData } = data;
		const account = await prisma.account.create({
			data: {
				...accountData,
			},
		});
		validIds(paymentAppIds) && await prisma.accountPaymentAppLink.createMany({
			data:
				paymentAppIds?.map(paymentAppId => ({
					accountId: account.id,
					paymentAppId,
				})) ?? [],
		});
		validIds(debitCardIds) && await prisma.accountDebitCardLink.createMany({
			data:
				debitCardIds?.map(debitCardId => ({
					accountId: account.id,
					debitCardId,
				})) ?? [],
		});
		const accountWithLinked = await this.getAccountWithLinked(account.id);
		if (!accountWithLinked) {
			throw new NotFoundError('Account not found after creating');
		}
		return accountWithLinked;
	}

	static async updateAccountWithLinked(data: UpdateAccountWithLinked): Promise<AccountWithLinked> {
		const {
			paymentApps: paymentAppIds,
			debitCards: debitCardIds,
			removePaymentApps,
			removeDebitCards,
			...accountData
		} = data;
		const account = await prisma.account.update({
			where: { id: accountData.id },
			data: {
				...accountData,
			},
		});

		validIds(paymentAppIds) &&
			(await prisma.accountPaymentAppLink.createMany({
				data:
					paymentAppIds?.map(paymentAppId => ({
						accountId: account.id,
						paymentAppId,
					})) ?? [],
			}));
		validIds(debitCardIds) &&
			(await prisma.accountDebitCardLink.createMany({
				data:
					debitCardIds?.map(debitCardId => ({
						accountId: account.id,
						debitCardId,
					})) ?? [],
			}));
		validIds(removePaymentApps) &&
			(await prisma.accountPaymentAppLink.deleteMany({
				where: {
					accountId: account.id,
					paymentAppId: {
						in: removePaymentApps,
					},
				},
			}));
		validIds(removeDebitCards) &&
			(await prisma.accountDebitCardLink.deleteMany({
				where: {
					accountId: account.id,
					debitCardId: {
						in: removeDebitCards,
					},
				},
			}));

		const accountWithLinked = await this.getAccountWithLinked(account.id);
		if (!accountWithLinked) {
			throw new NotFoundError('Account not found after updating');
		}
		return accountWithLinked;
	}
}
