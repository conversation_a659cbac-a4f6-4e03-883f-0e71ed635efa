import { BaseTableWithUser } from '../base';
import { UUID } from '../common';

export type {
  DebitCard,
  DebitCardBase,
  DebitCardWithRelations,
  CreateDebitCardInput,
  UpdateDebitCardInput,
  DebitCardFilterInput
};

/**
 * Base interface for DebitCard
 * Matches DebitCard model in Prisma schema
 */
interface DebitCardBase extends BaseTableWithUser {
  cardNumber: string | null;
  cardHolderName: string | null;
  bankName: string | null;
  note: string | null;
  accountId: UUID | null;
}

/**
 * DebitCard with relations
 */
interface DebitCardWithRelations extends DebitCardBase {
  account: AccountBase | null;
  accountDebitCardLinks: AccountDebitCardLink[];
}

/**
 * Union type for DebitCard
 */
type DebitCard = DebitCardBase | DebitCardWithRelations;

/**
 * Input for creating a new debit card
 */
interface CreateDebitCardInput {
  cardNumber?: string | null;
  cardHolderName?: string | null;
  bankName?: string | null;
  note?: string | null;
  accountId?: UUID | null;
  isActive?: boolean;
  userId: UUID;
}

/**
 * Input for updating an existing debit card
 */
interface UpdateDebitCardInput {
  id: UUID;
  cardNumber?: string | null;
  cardHolderName?: string | null;
  bankName?: string | null;
  note?: string | null;
  accountId?: UUID | null;
  isActive?: boolean;
}

/**
 * Input for filtering debit cards
 */
interface DebitCardFilterInput {
  search?: string;
  userId?: UUID;
  accountId?: UUID | null;
  bankName?: string;
  isActive?: boolean;
}

// Forward declarations to avoid circular imports
interface AccountBase {
  id: UUID;
  name: string;
  userId: UUID;
}

interface AccountDebitCardLink {
  id: UUID;
  accountId: UUID;
  debitCardId: UUID;
  isActive: boolean;
}
