import { BaseTableWithUser } from '../base';
import { UUID } from '../common';

export type {
  PaymentApp,
  PaymentAppBase,
  PaymentAppWithRelations,
  CreatePaymentAppInput,
  UpdatePaymentAppInput,
  PaymentAppFilterInput
};

/**
 * Base interface for PaymentApp
 * Matches PaymentApp model in Prisma schema
 */
interface PaymentAppBase extends BaseTableWithUser {
  name: string;
  description: string | null;
  color: string;
  accountName: string | null;
  phoneNumber: string | null;
}

/**
 * PaymentApp with relations
 */
interface PaymentAppWithRelations extends PaymentAppBase {
  accountPaymentAppLinks: AccountPaymentAppLink[];
}

/**
 * Union type for PaymentApp
 */
type PaymentApp = PaymentAppBase | PaymentAppWithRelations;

/**
 * Input for creating a new payment app
 */
interface CreatePaymentAppInput {
  name: string;
  description?: string | null;
  color?: string;
  accountName?: string | null;
  phoneNumber?: string | null;
  isActive?: boolean;
  userId: UUID;
}

/**
 * Input for updating an existing payment app
 */
interface UpdatePaymentAppInput {
  id: UUID;
  name?: string;
  description?: string | null;
  color?: string;
  accountName?: string | null;
  phoneNumber?: string | null;
  isActive?: boolean;
}

/**
 * Input for filtering payment apps
 */
interface PaymentAppFilterInput {
  search?: string;
  userId?: UUID;
  isActive?: boolean;
}

// Forward declaration to avoid circular import
interface AccountPaymentAppLink {
  id: UUID;
  accountId: UUID;
  paymentAppId: UUID;
  isActive: boolean;
}
