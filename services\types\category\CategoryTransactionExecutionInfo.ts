import { BaseTableWithUser } from '../base';
import { UUID } from '../common';

export type {
  CategoryTransactionExecutionInfo,
  CategoryTransactionExecutionInfoBase,
  CategoryTransactionExecutionInfoWithRelations,
  CreateCategoryTransactionExecutionInfoInput,
  UpdateCategoryTransactionExecutionInfoInput,
  CategoryTransactionExecutionInfoFilterInput
};

/**
 * Base interface for CategoryTransactionExecutionInfo
 * Matches CategoryTransactionExecutionInfo model in Prisma schema
 * Records the execution of a transaction within a category context
 */
interface CategoryTransactionExecutionInfoBase extends BaseTableWithUser {
  transactionId: UUID;
  categoryId: UUID;
  categoryGoalId: UUID | null;
  allocatedAmount: number | null; // Amount allocated to this category for this transaction
}

/**
 * CategoryTransactionExecutionInfo with relations
 */
interface CategoryTransactionExecutionInfoWithRelations extends CategoryTransactionExecutionInfoBase {
  transaction: TransactionBase;
  category: CategoryBase;
  categoryGoal: CategoryGoalBase | null;
}

/**
 * Union type for CategoryTransactionExecutionInfo
 */
type CategoryTransactionExecutionInfo = CategoryTransactionExecutionInfoBase | CategoryTransactionExecutionInfoWithRelations;

/**
 * Input for creating a new category transaction execution info
 */
interface CreateCategoryTransactionExecutionInfoInput {
  transactionId: UUID;
  categoryId: UUID;
  categoryGoalId?: UUID | null;
  allocatedAmount?: number | null;
  isActive?: boolean;
  userId: UUID;
}

/**
 * Input for updating an existing category transaction execution info
 */
interface UpdateCategoryTransactionExecutionInfoInput {
  id: UUID;
  categoryGoalId?: UUID | null;
  allocatedAmount?: number | null;
  isActive?: boolean;
}

/**
 * Input for filtering category transaction execution info
 */
interface CategoryTransactionExecutionInfoFilterInput {
  transactionId?: UUID;
  categoryId?: UUID;
  categoryGoalId?: UUID | null;
  userId?: UUID;
  isActive?: boolean;
}

// Forward declarations to avoid circular imports
interface TransactionBase {
  id: UUID;
  type: string;
  amount: number;
}

interface CategoryBase {
  id: UUID;
  name: string;
  type: string;
}

interface CategoryGoalBase {
  id: UUID;
  name: string | null;
  categoryId: UUID | null;
}

