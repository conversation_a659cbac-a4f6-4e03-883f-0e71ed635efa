import { AccountTypeType, UUID } from '../common';
import type { BaseTableActiveable } from '../base';

export type {
  AccountType,
  AccountTypeBase,
  AccountTypeWithRelations,
  CreateAccountTypeInput,
  UpdateAccountTypeInput,
  AccountTypeFilterInput
};

/**
 * Base account type interface
 * Matches AccountType model in Prisma schema
 */
interface AccountTypeBase extends BaseTableActiveable {
  name: string;
  description: string | null;
  color: string;
  type: AccountTypeType;
}

/**
 * Account type with relations
 */
interface AccountTypeWithRelations extends AccountTypeBase {
  accounts: AccountBase[];
}

/**
 * Union type for account type
 */
type AccountType = AccountTypeBase | AccountTypeWithRelations;

/**
 * Input for creating a new account type
 */
interface CreateAccountTypeInput {
  name: string;
  description?: string | null;
  color?: string;
  type?: AccountTypeType;
  isActive?: boolean;
}

/**
 * Input for updating an existing account type
 */
interface UpdateAccountTypeInput {
  id: UUID;
  name?: string;
  description?: string | null;
  color?: string;
  type?: AccountTypeType;
  isActive?: boolean;
}

/**
 * Input for filtering account types
 */
interface AccountTypeFilterInput {
  search?: string;
  type?: AccountTypeType;
  isActive?: boolean;
}

// Forward declaration to avoid circular import
interface AccountBase {
  id: UUID;
  name: string;
  accountTypeId: UUID;
}