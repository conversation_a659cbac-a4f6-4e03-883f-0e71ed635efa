import { BaseTableWithUser } from '../base';
import { UUID } from '../common';
import { ContactTypeBase } from './contact-type';
import { UserBase } from '../user';

/**
 * Base interface for Contact
 * Matches Contact model in Prisma schema
 */
export interface ContactBase extends BaseTableWithUser {
  name: string;
  description: string | null;
  contactTypeId: UUID;
  phone: string | null;
  email: string | null;
  address: string | null;
}

/**
 * Contact with relations
 */
export interface ContactWithRelations extends ContactBase {
  contactType: ContactTypeBase;
  user: UserBase;
  accounts: AccountBase[];
}

/**
 * Union type for Contact
 */
export type Contact = ContactBase | ContactWithRelations;

// Input types for Contact mutations

/**
 * Input for creating a new contact
 */
export interface CreateContactInput {
  name: string;
  description?: string | null;
  contactTypeId: UUID;
  phone?: string | null;
  email?: string | null;
  address?: string | null;
  isActive?: boolean;
  userId: UUID;
}

/**
 * Input for updating an existing contact
 */
export interface UpdateContactInput {
  id: UUID;
  name?: string;
  description?: string | null;
  contactTypeId?: UUID;
  phone?: string | null;
  email?: string | null;
  address?: string | null;
  isActive?: boolean;
}

/**
 * Input for filtering contacts
 */
export interface ContactFilterInput {
  search?: string;
  contactTypeId?: UUID;
  isActive?: boolean;
  hasPhone?: boolean;
  hasEmail?: boolean;
  hasAddress?: boolean;
}

// Forward declaration to avoid circular import
interface AccountBase {
  id: UUID;
  name: string;
  contactId: UUID | null;
}
