
import { BaseTableWithUser } from '../base';
import { DateTime, UUID } from '../common';

export type {
  CategoryPeriodRecord,
  CategoryPeriodRecordBase,
  CategoryPeriodRecordWithRelations,
  CreateCategoryPeriodRecordInput,
  UpdateCategoryPeriodRecordInput,
  CategoryPeriodRecordFilterInput
};

/**
 * Base interface for CategoryPeriodRecord
 * Matches CategoryPeriodRecord model in Prisma schema
 * Records the summary of category performance at the end of a period
 */
interface CategoryPeriodRecordBase extends BaseTableWithUser {
  categoryGoalId: UUID;
  categoryId: UUID;
  targetAmount: number | null;
  reachedAmount: number | null;
  periodStart: DateTime | null;
  periodEnd: DateTime | null;
}

/**
 * CategoryPeriodRecord with relations
 */
interface CategoryPeriodRecordWithRelations extends CategoryPeriodRecordBase {
  categoryGoal: CategoryGoalBase;
  category: CategoryBase;
}

/**
 * Union type for CategoryPeriodRecord
 */
type CategoryPeriodRecord = CategoryPeriodRecordBase | CategoryPeriodRecordWithRelations;

/**
 * Input for creating a new category period record
 */
interface CreateCategoryPeriodRecordInput {
  categoryGoalId: UUID;
  categoryId: UUID;
  targetAmount?: number | null;
  reachedAmount?: number | null;
  periodStart?: DateTime | null;
  periodEnd?: DateTime | null;
  isActive?: boolean;
  userId: UUID;
}

/**
 * Input for updating an existing category period record
 */
interface UpdateCategoryPeriodRecordInput {
  id: UUID;
  targetAmount?: number | null;
  reachedAmount?: number | null;
  periodStart?: DateTime | null;
  periodEnd?: DateTime | null;
  isActive?: boolean;
}

/**
 * Input for filtering category period records
 */
interface CategoryPeriodRecordFilterInput {
  categoryGoalId?: UUID;
  categoryId?: UUID;
  userId?: UUID;
  periodStart?: DateTime;
  periodEnd?: DateTime;
  isActive?: boolean;
}

// Forward declarations to avoid circular imports
interface CategoryGoalBase {
  id: UUID;
  name: string | null;
  categoryId: UUID | null;
}

interface CategoryBase {
  id: UUID;
  name: string;
  type: string;
}