
import { DateTime, UUID } from './common';

export type { BaseTable, BaseTableActiveable, BaseTableWithUser };

/**
 * Base interface for all database tables
 * Matches the common structure of all Prisma models
 */
interface BaseTable {
  id: UUID;
  createdAt: DateTime;
  updatedAt: DateTime;
  deletedAt: DateTime | null;
}

/**
 * Base interface for tables with isActive flag
 * Used by most entities that can be soft-deleted
 */
interface BaseTableActiveable extends BaseTable {
  isActive: boolean;
}

/**
 * Base interface for user-owned entities
 * Used by entities that belong to a specific user
 */
interface BaseTableWithUser extends BaseTableActiveable {
  userId: UUID;
}

