
import { BaseTableWithUser } from './base';
import { DateTime, TransactionType, UUID } from './common';

export type {
  BulkTransactionInput,
  CreateTransactionInput,
  Transaction,
  TransactionBase,
  TransactionFilterInput,
  TransactionResponse,
  TransactionWithRelations,
  UpdateTransactionInput
};

/**
 * Base interface for Transaction
 * Matches Transaction model in Prisma schema
 */
interface TransactionBase extends BaseTableWithUser {
  type: TransactionType;
  amount: number;
  date: DateTime;
  description: string | null;
  location: string | null;
  note: string | null;
  attachments: string[];
  tags: string[];
  isCleared: boolean;
  isReconciled: boolean;

  // Account relations
  categoryId: UUID | null;
  fromAccountId: UUID | null;
  toAccountId: UUID | null;
  connectedFromAccountId: UUID | null;
  connectedToAccountId: UUID | null;
  connectedFromDebitCardId: UUID | null;
  connectedToDebitCardId: UUID | null;
  connectedFromPaymentAppId: UUID | null;
  connectedToPaymentAppId: UUID | null;
}



/**
 * Transaction with relations
 */
interface TransactionWithRelations extends TransactionBase {
  category: CategoryBase | null;
  fromAccount: AccountBase | null;
  toAccount: AccountBase | null;
  connectedFromAccount: AccountBase | null;
  connectedToAccount: AccountBase | null;
  connectedFromDebitCard: DebitCardBase | null;
  connectedToDebitCard: DebitCardBase | null;
  connectedFromPaymentApp: PaymentAppBase | null;
  connectedToPaymentApp: PaymentAppBase | null;
}

/**
 * Union type for Transaction
 */
type Transaction = TransactionBase | TransactionWithRelations;

/**
 * Input type for creating a new transaction
 */
interface CreateTransactionInput {
  type: TransactionType;
  amount: number;
  date: DateTime;
  description?: string | null;
  location?: string | null;
  note?: string | null;
  attachments?: string[];
  tags?: string[];
  isCleared?: boolean;
  isReconciled?: boolean;
  categoryId?: UUID | null;
  fromAccountId?: UUID | null;
  toAccountId?: UUID | null;
  connectedFromAccountId?: UUID | null;
  connectedToAccountId?: UUID | null;
  connectedFromDebitCardId?: UUID | null;
  connectedToDebitCardId?: UUID | null;
  connectedFromPaymentAppId?: UUID | null;
  connectedToPaymentAppId?: UUID | null;
  isActive?: boolean;
  userId: UUID;
}

/**
 * Input type for updating a transaction
 */
interface UpdateTransactionInput {
  id: UUID;
  type?: TransactionType;
  amount?: number;
  date?: DateTime;
  description?: string | null;
  location?: string | null;
  note?: string | null;
  attachments?: string[];
  tags?: string[];
  isCleared?: boolean;
  isReconciled?: boolean;
  categoryId?: UUID | null;
  fromAccountId?: UUID | null;
  toAccountId?: UUID | null;
  connectedFromAccountId?: UUID | null;
  connectedToAccountId?: UUID | null;
  connectedFromDebitCardId?: UUID | null;
  connectedToDebitCardId?: UUID | null;
  connectedFromPaymentAppId?: UUID | null;
  connectedToPaymentAppId?: UUID | null;
  isActive?: boolean;
}

/**
 * Input type for transaction filters
 */
interface TransactionFilterInput {
  search?: string;
  type?: TransactionType;
  minAmount?: number;
  maxAmount?: number;
  startDate?: DateTime;
  endDate?: DateTime;
  categoryId?: UUID;
  fromAccountId?: UUID;
  toAccountId?: UUID;
  isCleared?: boolean;
  isReconciled?: boolean;
  isActive?: boolean;
  userId?: UUID;
}

/**
 * Input type for bulk transaction operations
 */
interface BulkTransactionInput {
  ids: UUID[];
  isCleared?: boolean;
  isReconciled?: boolean;
  categoryId?: UUID;
  isActive?: boolean;
}

/**
 * Response type for transaction operations
 */
interface TransactionResponse {
  success: boolean;
  message?: string;
  transaction?: Transaction;
  errors?: Array<{ message: string; field?: string }>;
}

// Forward declarations to avoid circular imports
interface CategoryBase {
  id: UUID;
  name: string;
  type: string;
}

interface AccountBase {
  id: UUID;
  name: string;
  userId: UUID;
}

interface DebitCardBase {
  id: UUID;
  cardNumber: string | null;
  userId: UUID;
}

interface PaymentAppBase {
  id: UUID;
  name: string;
  userId: UUID;
}


